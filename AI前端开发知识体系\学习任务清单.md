# 前端开发者 · AI 对话应用学习路线（Markdown 版）

> 适用人群：已具备前端工程化基础，目标在 **6 周** 内做出可上线 MVP，**12 周** 内扩展到生产级，**24 周** 内深耕性能、安全与成本优化。  
> 输出格式：阶段 → 周 → 任务清单（含前置依赖 & 最小可交付物）。可直接复制到任何 Markdown 工具中打勾追踪。

---

## 📌 阶段 0 · 环境热身（1 天）
| # | 任务 | 交付物 | 备注 |
|---|---|---|---|
| 0-1 | 注册 Vercel / Netlify 账号 | 可访问的 Dashboard | 后续一键部署 |
| 0-2 | 安装 Node 20 + pnpm + GitHub CLI | `node -v` 输出 ≥20 | 确保后续脚手架顺利 |
| 0-3 | fork 官方示例 [vercel-labs/ai-chat](https://github.com/vercel-labs/ai-chat) | 本地 `pnpm dev` 成功 | 获得基础代码模板 |

---

## 🚀 阶段 1 · MVP 冲刺（第 1-2 周）
> 目标：能在手机浏览器完成一次连续对话，流式返回答案。

| 周 | 知识点 | 技术要点 | 最小可交付物 |
|---|---|---|---|
| 1-1 | Chat 容器与虚拟滚动 | `react-window` 或 `@tanstack/react-virtual` | 1000 条消息不卡顿 |
| 1-2 | 消息状态机 | `idle / loading / success / error` 四状态 UI | 失败消息可重试 |
| 1-3 | 打字机效果 | `ReadableStream` + 增量 DOM 更新 | 逐字打印回答 |
| 1-4 | SSE 流式通信 | `fetch` + `TextDecoder` + `ndjson` | 网络断开后 3 秒内自动重连 |
| 1-5 | 前端敏感词过滤 | RegExp + trie 树 | 输入“政治”立即提示违规 |
| 1-6 | 上下文记忆 | `localStorage` 缓存最近 10 轮对话 | 刷新页面后对话不丢 |

---

## 🏗️ 阶段 2 · 生产级 UI（第 3-6 周）
> 目标：支持富文本、代码运行、主题切换、文件上传、限流提示。

| 周 | 知识点 | 技术要点 | 最小可交付物 |
|---|---|---|---|
| 3-1 | Markdown 安全渲染 | `markdown-it` + `DOMPurify` | XSS 脚本被过滤 |
| 3-2 | 代码高亮 & 运行 | `Prism.js` + `Sandpack` | Python 代码块一键执行 |
| 3-3 | 文件上传与预览 | `react-dropzone` + 浏览器压缩 | 拖拽 PNG → 预览 & 上传进度 |
| 3-4 | 主题系统 | `next-themes` + CSS 变量 | 深色 / 浅色一键切换并持久化 |
| 4-1 | 统一 LLM 网关 | Vercel AI SDK `openai`, `anthropic` provider | 支持 OpenAI / Claude 切换 |
| 4-2 | 限流与提示 | Upstash `@upstash/ratelimit` | 页面提示“今日剩余 10 次” |
| 5-1 | 错误监控 | Sentry + Vercel Analytics | 线上报错可追踪堆栈 |
| 6-1 | 单元 & E2E 测试 | Vitest + Playwright | `pnpm test --ui` 通过 |

---

## 🔌 阶段 3 · 能力扩展（第 7-12 周）
> 目标：支持函数调用、语音、图片识别、插件系统、RAG。

| 周 | 知识点 | 技术要点 | 最小可交付物 |
|---|---|---|---|
| 7-1 | Function Calling | LangChain `ChatOpenAI.bind({functions})` | 输入“北京天气”→ 返回天气卡片 |
| 7-2 | 多 Agent 路由 | LangGraph / CrewAI JS 版 | 任务拆分 → 并行 → 汇总 |
| 8-1 | 语音输入输出 | Web Speech API | 长按空格录音，松手播报回答 |
| 9-1 | 图片识别 | OpenAI gpt-4-vision-preview | 上传截图→返回图中文字 |
| 10-1 | 插件系统 | iframe 沙箱 + postMessage | 可安装“Mermaid 图表”插件 |
| 11-1 | RAG 检索 | LangChain + pgvector | 上传 PDF→提问带页码引用 |
| 12-1 | 性能监控 | Web-Vitals + Lighthouse CI | 首屏 < 2.5s，Lighthouse 90+ |

---

## ⚙️ 阶段 4 · 深耕赛道（第 13-24 周，任选其二）
| 赛道 | 关键词 | 关键产出 |
|---|---|---|
| 🧠 端侧推理 | WebGPU、Transformers.js、量化 | 浏览器内跑 7B 模型，离线回答 |
| 🔐 安全合规 | CSP、Trusted Types、数据脱敏 | 通过 SOC 2 / GDPR 检查 |
| 💰 成本优化 | 自建网关、缓存、模型路由 | 成本下降 30%，SLA 99.9% |
| 📊 数据科学 | 对话漏斗、留存、Prompt A/B | 找到高转化 Prompt |

---

## ✅ 速查清单（复制打勾）
- [ ] 阶段 0：环境 OK  
- [ ] 阶段 1：手机可连续对话  
- [ ] 阶段 2：富文本 + 主题 + 限流  
- [ ] 阶段 3：函数调用 / 语音 / 图片 / RAG  
- [ ] 阶段 4：选定深耕赛道并闭环

---

## 📦 资源索引
| 名称 | 链接 |
|---|---|
| 官方示例 | https://github.com/vercel-labs/ai-chat |
| Vercel AI SDK 文档 | https://sdk.vercel.ai/docs |
| LangChain JS | https://js.langchain.com/docs |
| React 虚拟滚动 | https://tanstack.com/virtual/latest |
| Web Speech API | https://developer.mozilla.org/en-US/docs/Web/API/Web_Speech_API |

---

